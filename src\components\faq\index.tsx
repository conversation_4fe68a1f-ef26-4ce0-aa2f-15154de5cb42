'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import { containerVariants, itemVariants } from '@/constants/faq';
import { Search, X, MessageCircle, ArrowRight } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const FAQ = () => {
  const { t } = useTranslation('faq');
  const [searchQuery, setSearchQuery] = useState('');

  // FAQ questions data memoized to prevent recreation on each render
  const faqQuestions = useMemo(
    () => [
      {
        id: 'q1',
        question: t('questions.q1.question'),
        answer: t('questions.q1.answer'),
      },
      {
        id: 'q2',
        question: t('questions.q2.question'),
        answer: t('questions.q2.answer'),
      },
      {
        id: 'q3',
        question: t('questions.q3.question'),
        answer: t('questions.q3.answer'),
      },
      {
        id: 'q4',
        question: t('questions.q4.question'),
        answer: t('questions.q4.answer'),
      },
      {
        id: 'q5',
        question: t('questions.q5.question'),
        answer: t('questions.q5.answer'),
      },
    ],
    [t]
  );

  // Filter questions based on search query
  const filteredQuestions = useMemo(() => {
    if (!searchQuery.trim()) return faqQuestions;

    const query = searchQuery.toLowerCase();
    return faqQuestions.filter(
      (item) =>
        item.question.toLowerCase().includes(query) ||
        item.answer.toLowerCase().includes(query)
    );
  }, [searchQuery, faqQuestions]);

  const clearSearch = () => {
    setSearchQuery('');
  };

  return (
    <div className="from-background via-muted/30 to-background min-h-screen bg-gradient-to-br">
      <div className="container mx-auto max-w-4xl px-4 py-16">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-12"
        >
          {/* Header Section */}
          <motion.div variants={itemVariants} className="space-y-6 text-center">
            <div className="space-y-4">
              <motion.h1
                className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-4xl font-bold text-transparent md:text-5xl lg:text-6xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                {t('page.title')}
              </motion.h1>
              <motion.p
                className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed md:text-xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                {t('page.subtitle')}
              </motion.p>
            </div>
          </motion.div>

          {/* Search Section */}
          <motion.div
            variants={itemVariants}
            className="relative mx-auto max-w-2xl"
          >
            <div className="relative">
              <Search className="text-muted-foreground absolute top-1/2 left-4 h-5 w-5 -translate-y-1/2 transform" />
              <Input
                type="text"
                placeholder={t('page.search.placeholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-card/80 border-border/50 focus:border-primary/50 focus:ring-primary/20 h-14 pr-12 pl-12 text-lg backdrop-blur-sm transition-all duration-300"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="hover:bg-muted/80 absolute top-1/2 right-2 h-8 w-8 -translate-y-1/2 transform p-0"
                  aria-label={t('page.search.clear')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </motion.div>

          {/* FAQ Content */}
          <motion.div variants={itemVariants} className="space-y-6">
            {filteredQuestions.length > 0 ? (
              <Card className="bg-card/80 border-border/50 shadow-lg backdrop-blur-sm">
                <CardContent className="p-6">
                  <Accordion type="single" collapsible className="space-y-4">
                    {filteredQuestions.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                      >
                        <AccordionItem
                          value={item.id}
                          className="border-border/30 hover:bg-muted/30 rounded-lg px-4 transition-colors duration-200"
                        >
                          <AccordionTrigger className="group text-left hover:no-underline">
                            <span className="text-foreground group-hover:text-primary font-semibold transition-colors duration-200">
                              {item.question}
                            </span>
                          </AccordionTrigger>
                          <AccordionContent className="text-muted-foreground pt-2 leading-relaxed">
                            <div className="prose prose-sm dark:prose-invert max-w-none">
                              {item.answer}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </motion.div>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="py-12 text-center"
              >
                <div className="space-y-4">
                  <div className="bg-muted/50 mx-auto flex h-16 w-16 items-center justify-center rounded-full">
                    <Search className="text-muted-foreground h-8 w-8" />
                  </div>
                  <h3 className="text-foreground text-xl font-semibold">
                    {t('page.search.no_results')}
                  </h3>
                  <Button
                    variant="outline"
                    onClick={clearSearch}
                    className="mt-4"
                  >
                    {t('page.search.clear')}
                  </Button>
                </div>
              </motion.div>
            )}
          </motion.div>

          {/* Contact CTA Section */}
          <motion.div variants={itemVariants} className="pt-8">
            <Card className="from-primary/10 via-secondary/10 to-accent/10 border-primary/20 bg-gradient-to-r backdrop-blur-sm">
              <CardContent className="space-y-6 p-8 text-center">
                <div className="space-y-4">
                  <div className="bg-primary/20 mx-auto flex h-16 w-16 items-center justify-center rounded-full">
                    <MessageCircle className="text-primary h-8 w-8" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-foreground text-2xl font-bold">
                      {t('contact.title')}
                    </h3>
                    <p className="text-muted-foreground mx-auto max-w-md">
                      {t('contact.description')}
                    </p>
                  </div>
                </div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    size="lg"
                    className="from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-primary-foreground bg-gradient-to-r shadow-lg transition-all duration-300 hover:shadow-xl"
                    asChild
                  >
                    <a
                      href="/contact"
                      className="inline-flex items-center gap-2"
                    >
                      {t('contact.button')}
                      <ArrowRight className="h-4 w-4" />
                    </a>
                  </Button>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default FAQ;
